import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/sms/presentation/widgets/phone_row_widget.dart';
import '../../controllers/sms_controller.dart';
import '../widgets/recipient_chip.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class SmsCreateScreen extends StatelessWidget {
  const SmsCreateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Create SMS')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message input
            CustomTextField(
              controller: controller.messageController,
              labelText: 'Message',
              hintText: 'Enter your message here...',

              maxLines: 5,
            ),
            const SizedBox(height: 16),
            // Character count
            Text(
              '${controller.messageController.text.length} characters',
              style: TextStyle(
                color:
                    controller.messageController.text.length > 160
                        ? Colors.red
                        : Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            // Recipients section
            const Text(
              'Recipients',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            // Add recipient input with integrated paste functionality
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: controller.recipientController,
                    labelText: 'Phone Number',
                    hintText: 'e.g. +254712345678',
                    prefixIcon: const Icon(Icons.phone),
                    suffixIcon: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: CustomButton(
                        onPressed: () {
                          if (controller.recipientController.text.isNotEmpty) {
                            controller.addRecipient(
                              controller.recipientController.text,
                            );
                          }
                        },
                        icon: Icon(Icons.add),
                        label: Text('Add'),
                      ),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                PhoneRowWidget(),
              ],
            ),

            // Group selection moved to PhoneRowWidget
            const SizedBox(height: 16),
            // Recipients list
            Obx(
              () => Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children:
                    controller.recipients
                        .map(
                          (phone) => RecipientChip(
                            phone: phone,
                            onDeleted: () => controller.removeRecipient(phone),
                          ),
                        )
                        .toList(),
              ),
            ),
            const Spacer(),
            // Send buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Obx(
                    () => CustomButton(
                      onPressed: () async {
                        if (controller.isSending.value) return;

                        // Validate message
                        if (controller.messageController.text.trim().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Please enter a message'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Use new confirmation flow
                        await controller.sendSmsWithConfirmation(
                          context,
                          controller.messageController.text,
                          controller.recipients,
                        );
                      },
                      isLoading: controller.isSending.value,
                      label: const Text('Send SMS'),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
