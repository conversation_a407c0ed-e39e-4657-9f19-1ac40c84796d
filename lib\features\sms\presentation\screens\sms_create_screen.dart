import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/sms/presentation/widgets/phone_row_widget.dart';
import 'package:onechurch/features/members/presentation/screens/members_screen/members_screen.dart';
import 'package:onechurch/features/group/presentation/view_groups.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:onechurch/features/group/controllers/group_controller.dart';
import '../../controllers/sms_controller.dart';
import '../widgets/recipient_chip.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class SmsCreateScreen extends StatelessWidget {
  const SmsCreateScreen({super.key});

  Future<bool?> _showPreviewDialog(
    BuildContext context,
    Map<String, dynamic> result,
  ) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('SMS Preview'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Message Details',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDetailRow(
                            'Price per SMS:',
                            'Ksh ${result['data']['price_per_unit']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Characters:',
                            '${result['data']['total_characters']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Recipients:',
                            '${result['data']['total_recipients']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Number of SMS:',
                            '${result['data']['total_sms']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Cost:',
                            'Ksh ${result['data']['total_charges']}',
                            valueStyle: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Message Preview:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Text('${result['data']['message']}'),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              CustomButton(
                onPressed: () => Navigator.pop(context, true),
                label: const Text('Send'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value, {TextStyle? valueStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [Text(label), Text(value, style: valueStyle)],
    );
  }

  Widget _buildEntitySpecificUI(
    BuildContext context,
    SmsController controller,
  ) {
    switch (controller.selectedEntityType.value) {
      case 'PHONE_NUMBER':
        return _buildPhoneNumberUI(controller);
      case 'MEMBER_ID':
        return _buildMemberSelectionUI(context, controller);
      case 'GROUP_ID':
        return _buildGroupSelectionUI(context, controller);
      default:
        return _buildPhoneNumberUI(controller);
    }
  }

  Widget _buildPhoneNumberUI(SmsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recipients',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        // Add recipient input with integrated paste functionality
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: controller.recipientController,
                labelText: 'Phone Number',
                hintText: 'e.g. +254712345678',
                prefixIcon: const Icon(Icons.phone),
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FilledButton.icon(
                    onPressed: () {
                      if (controller.recipientController.text.isNotEmpty) {
                        controller.addRecipient(
                          controller.recipientController.text,
                        );
                      }
                    },
                    icon: Icon(Icons.add),
                    label: Text('Add'),
                  ),
                ),
                keyboardType: TextInputType.phone,
              ),
            ),
            PhoneRowWidget(),
          ],
        ),
        const SizedBox(height: 16),
        // Recipients list
        Obx(
          () => Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children:
                controller.recipients
                    .map(
                      (phone) => RecipientChip(
                        phone: phone,
                        onDeleted: () => controller.removeRecipient(phone),
                      ),
                    )
                    .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildMemberSelectionUI(
    BuildContext context,
    SmsController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Selected Members',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            FilledButton.icon(
              onPressed: () async {
                final memberController = Get.find<MemberController>();
                memberController.selectedMembers.clear();

                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => const MemberViewScreen(isSelect: true),
                  ),
                );

                if (result != null) {
                  controller.selectedMemberIds.clear();
                  for (final member in memberController.selectedMembers) {
                    if (member.id != null) {
                      controller.selectedMemberIds.add(member.id!);
                    }
                  }
                }
              },
              icon: const Icon(Icons.person_add),
              label: const Text('Select Members'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Obx(
          () =>
              controller.selectedMemberIds.isEmpty
                  ? const Text('No members selected')
                  : Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children:
                        controller.selectedMemberIds
                            .map(
                              (memberId) => Chip(
                                label: Text('Member: $memberId'),
                                onDeleted:
                                    () => controller.selectedMemberIds.remove(
                                      memberId,
                                    ),
                              ),
                            )
                            .toList(),
                  ),
        ),
      ],
    );
  }

  Widget _buildGroupSelectionUI(
    BuildContext context,
    SmsController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Selected Groups',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            FilledButton.icon(
              onPressed: () async {
                final groupController = Get.find<GroupController>();
                groupController.clearSelectedGroups();

                final result = await Navigator.push<List<String>>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ViewGroupsGrid(isSelect: true),
                  ),
                );

                if (result != null) {
                  controller.selectedGroupIds.clear();
                  controller.selectedGroupIds.addAll(result);
                }
              },
              icon: const Icon(Icons.group_add),
              label: const Text('Select Groups'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Obx(
          () =>
              controller.selectedGroupIds.isEmpty
                  ? const Text('No groups selected')
                  : Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children:
                        controller.selectedGroupIds
                            .map(
                              (groupId) => Chip(
                                label: Text('Group: $groupId'),
                                onDeleted:
                                    () => controller.selectedGroupIds.remove(
                                      groupId,
                                    ),
                              ),
                            )
                            .toList(),
                  ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Create SMS')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message input
            CustomTextField(
              controller: controller.messageController,
              labelText: 'Message',
              hintText: 'Enter your message here...',

              maxLines: 5,
            ),
            const SizedBox(height: 16),
            // Character count
            Text(
              '${controller.messageController.text.length} characters',
              style: TextStyle(
                color:
                    controller.messageController.text.length > 160
                        ? Colors.red
                        : Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            // Entity Type selection
            const Text(
              'Entity Type',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Obx(
              () => DropdownButtonFormField<String>(
                value: controller.selectedEntityType.value,
                decoration: const InputDecoration(
                  labelText: 'Select Entity Type',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items:
                    controller.entityTypes.map((String entityType) {
                      return DropdownMenuItem<String>(
                        value: entityType,
                        child: Text(entityType.replaceAll('_', ' ')),
                      );
                    }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    controller.setEntityType(newValue);
                  }
                },
              ),
            ),
            const SizedBox(height: 24),
            // Conditional UI based on entity type
            Obx(() => _buildEntitySpecificUI(context, controller)),
            const Spacer(),
            // Send buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Obx(
                    () => CustomButton(
                      onPressed: () async {
                        if (controller.isSending.value) return;

                        // Validate message
                        if (controller.messageController.text.trim().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Please enter a message'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Use new confirmation flow
                        await controller.sendSmsWithConfirmation(
                          context,
                          controller.messageController.text,
                          controller.recipients,
                        );
                      },
                      isLoading: controller.isSending.value,
                      label: const Text('Send Preview'),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
